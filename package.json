{"name": "aba-cadabra-front", "version": "0.1.0", "private": true, "scripts": {"serve": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "build": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "lint": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service lint", "deploy": "firebase deploy"}, "dependencies": {"@popperjs/core": "^2.0.6", "axios": "^0.21.2", "axios-jsonp": "^1.0.4", "core-js": "^3.4.3", "dompurify": "^2.0.8", "firebase": "^7.15.4", "ftellipsis": "^0.2.3", "lodash.findlastindex": "^4.6.0", "luxon": "^1.22.0", "pdfjs-dist": "^2.4.456", "pica": "^5.1.0", "slugify": "^1.4.0", "tiptap": "^1.32.2", "tiptap-extensions": "^1.35.2", "vue": "^2.6.10", "vue-click-outside": "^1.0.7", "vue-date-pick": "^1.2.1", "vue-datetime": "^1.0.0-beta.11", "vue-expandable-text-line": "^2.0.8", "vue-input-autowidth": "^1.0.9", "vue-lazyload": "^1.3.3", "vue-renderless-calendar": "^2.0.0", "vue-router": "^3.1.3", "vue-smooth-reflow": "^0.1.12", "vue-transition-expand": "0.0.5", "vue-vimeo-player": "^0.1.0", "vue2-google-maps": "^0.10.7", "vuedraggable": "^2.23.2", "vuex": "^3.1.2", "weekstart": "^1.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.2.3", "@vue/eslint-config-standard": "^4.0.0", "@vue/runtime-dom": "^3.3.4", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "sass": "^1.23.7", "sass-loader": "^8.0.0", "tailwindcss": "^1.2.0", "vue-cli-plugin-vuetify": "^2.0.2", "vue-template-compiler": "^2.6.10", "worker-loader": "^2.0.0"}}