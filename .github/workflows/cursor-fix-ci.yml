name: Fix CI Failures

on:
  workflow_run:
    workflows: [Deploy Preview]
    types: [completed]

permissions:
  contents: write
  pull-requests: write
  actions: read

jobs:
  attempt-fix:
    if: >-
      ${{ github.event.workflow_run.conclusion == 'failure' && github.event.workflow_run.name != 'Fix CI Failures' }}
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Cursor CLI
        run: |
          curl https://cursor.com/install -fsS | bash
          echo "$HOME/.cursor/bin" >> $GITHUB_PATH

      - name: Configure git identity
        run: |
          git config user.name "Cursor Agent"
          git config user.email "<EMAIL>"

      - name: Fix CI failure
        env:
          CURSOR_API_KEY: ${{ secrets.CURSOR_API_KEY }}
          MODEL: gpt-5
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          BR<PERSON>CH_PREFIX: ci-fix
        run: |
          cursor-agent --force --model "$MODEL" --output-format=text -p "You are operating in a GitHub Actions runner.

          The GitHub CLI is available as `gh` and authenticated via `GH_TOKEN`. Git is available. You have write access to repository contents and can comment on pull requests, but you must not create or edit PRs directly.

          # Context:
          - Repo: ${{ github.repository }}
          - Owner: ${{ github.repository_owner }}
          - Workflow Run ID: ${{ github.event.workflow_run.id }}
          - Workflow Run URL: ${{ github.event.workflow_run.html_url }}
          - Fix Branch Prefix: ${{ env.BRANCH_PREFIX }}

          # Goal:
          - Implement an end-to-end CI fix flow driven by the failing PR, creating a separate persistent fix branch and proposing a quick-create PR back into the original PR's branch.

          # Requirements:
          1) Identify the PR associated with the failed workflow run and determine its base and head branches. Let HEAD_REF be the PR's head branch (the contributor/origin branch).
          2) Maintain a persistent fix branch for this PR head using the Fix Branch Prefix from Context. Create it if missing, update it otherwise, and push changes to origin.
          3) Attempt to resolve the CI failure by making minimal, targeted edits consistent with the repo's style. Keep changes scoped and safe.
          4) You do NOT have permission to create PRs. Instead, post or update a single natural-language PR comment (1–2 sentences) that briefly explains the CI fix and includes an inline compare link to quick-create a PR.

          # Inputs and conventions:
          - Use `gh api`, `gh run view`, `gh pr view`, `gh pr diff`, `gh pr list`, `gh run download`, and git commands as needed to discover the failing PR and branches.
          - Avoid duplicate comments; if a previous bot comment exists, update it instead of posting a new one.
          - If no actionable fix is possible, make no changes and post no comment.

          # Deliverables when updates occur:
          - Pushed commits to the persistent fix branch for this PR head.
          - A single natural-language PR comment on the original PR that includes the inline compare link above."
