name: Deploy Preview

on:
  pull_request:
    paths:
      - '.github/workflows/deploy-preview.yml'
      - 'src/**'
      - 'package.json'
jobs:
  build_and_preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v5
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          npx browserslist@latest --update-db

      - name: Build
        run: npm run build

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}"
          channelId: ${{ github.ref_name }}
