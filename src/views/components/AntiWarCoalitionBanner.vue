<template>
    <a class="anti-war-coalition-banner" href="https://antiwarcoalition.art/texts" target="_blank">
        <svg
            class="awc-banner-full"
            width="100%"
            height="100%"
            viewBox="0 0 300 96"
            version="1.1" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            xml:space="preserve"
            xmlns:serif="http://www.serif.com/"
            style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
            <g transform="matrix(0.857143,0,0,0.857143,1.32751e-14,-266.571)">
                <g>
                    <g transform="matrix(0.585105,0,0,0.280697,1.22742,269.452)">
                        <rect x="255.121" y="148.017" width="85.74" height="315.881" style="fill:rgb(1,255,1);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,2.35969e-05,269.452)">
                        <rect x="170.079" y="148.017" width="85.039" height="315.881" style="fill:rgb(0,254,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.28439,4.44527e-06,268.905)">
                        <rect x="-0" y="148.018" width="85.039" height="315.881" style="fill:rgb(204,204,204);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,5.89923e-06,269.452)">
                        <rect x="85.039" y="148.017" width="85.039" height="315.881" style="fill:rgb(255,255,1);"/>
                    </g>
                    <g transform="matrix(0.592782,0,0,0.280697,-2.384,269.452)">
                        <rect x="340.57" y="148.017" width="84.629" height="315.881" style="fill:rgb(255,0,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,-1.16829,269.452)">
                        <rect x="425.2" y="148.017" width="85.039" height="315.881" style="fill:rgb(254,0,0);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,-1.16831,269.452)">
                        <rect x="510.239" y="148.017" width="85.039" height="315.881" style="fill:rgb(0,0,254);"/>
                    </g>
                    <g transform="matrix(0.585105,0,0,0.0738675,1.22742,388.733)">
                        <rect x="255.121" y="148.017" width="85.74" height="315.881" style="fill:rgb(24,24,24);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,2.35969e-05,388.733)">
                        <rect x="170.079" y="148.017" width="85.039" height="315.881" style="fill:rgb(255,0,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,4.44527e-06,388.733)">
                        <rect x="-0" y="148.018" width="85.039" height="315.881" style="fill:rgb(3,38,204);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,5.89923e-06,388.733)">
                        <rect x="85.039" y="148.017" width="85.039" height="315.881" style="fill:rgb(24,24,24);"/>
                    </g>
                    <g transform="matrix(0.592782,0,0,0.0738675,-2.384,388.733)">
                        <rect x="340.57" y="148.017" width="84.629" height="315.881" style="fill:rgb(0,254,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,-1.16829,388.733)">
                        <rect x="425.2" y="148.017" width="85.039" height="315.881" style="fill:rgb(24,24,24);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,-1.16831,388.733)">
                        <rect x="510.239" y="148.017" width="85.039" height="315.881" style="fill:rgb(203,203,203);"/>
                    </g>
                </g>
            </g>
            <g id="_-anti-war--coalition-art-" serif:id="(anti•war ¶coalition•art)" transform="matrix(11.7433,0,0,11.7433,-2975.67,-6911.76)">
                <path d="M256.522,590.867L255.715,590.867L255.527,591.461L255.351,591.461L256.041,589.332L256.199,589.332L256.884,591.461L256.709,591.461L256.522,590.867ZM255.764,590.698L256.472,590.698L256.12,589.591L255.764,590.698Z" style="fill-rule:nonzero;"/>
                <path d="M258.672,591.464L258.502,591.464L257.48,589.676L257.48,591.464L257.309,591.464L257.309,589.335L257.48,589.335L258.502,591.126L258.502,589.335L258.672,589.335L258.672,591.464Z" style="fill:rgb(16,0,255);fill-rule:nonzero;"/>
                <path d="M260.545,589.505L259.865,589.505L259.865,591.464L259.694,591.464L259.694,589.505L259.012,589.505L259.012,589.335L260.545,589.335L260.545,589.505Z" style="fill:rgb(16,0,255);fill-rule:nonzero;"/>
                <path d="M261.141,589.335L262.419,589.335L262.419,589.505L261.908,589.505L261.908,591.294L262.419,591.294L262.419,591.461L261.141,591.461L261.141,591.294L261.737,591.294L261.737,589.505L261.141,589.505L261.141,589.335Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <path d="M263.192,590.368C263.193,590.329 263.2,590.294 263.214,590.261C263.227,590.228 263.246,590.2 263.27,590.176C263.294,590.152 263.322,590.134 263.356,590.121C263.39,590.108 263.427,590.101 263.468,590.101C263.508,590.101 263.546,590.108 263.579,590.121C263.613,590.134 263.642,590.152 263.666,590.176C263.691,590.2 263.71,590.228 263.723,590.261C263.736,590.294 263.744,590.329 263.745,590.368L263.745,590.432C263.744,590.471 263.736,590.506 263.723,590.538C263.71,590.571 263.691,590.598 263.667,590.622C263.643,590.645 263.615,590.663 263.581,590.676C263.547,590.689 263.51,590.695 263.469,590.695C263.428,590.695 263.391,590.689 263.358,590.676C263.324,590.663 263.295,590.645 263.27,590.622C263.246,590.598 263.227,590.571 263.214,590.538C263.2,590.506 263.193,590.471 263.192,590.432L263.192,590.368Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <path d="M264.937,590.978L264.949,591.127L264.966,590.978L265.28,589.332L265.434,589.332L265.748,590.978L265.766,591.132L265.779,590.978L266,589.332L266.165,589.332L265.862,591.461L265.686,591.461L265.357,589.686L265.027,591.461L264.851,591.461L264.547,589.332L264.715,589.332L264.937,590.978Z" style="fill:rgb(167,0,231);fill-rule:nonzero;"/>
                <path d="M267.422,590.867L266.615,590.867L266.421,591.464L266.251,591.464L266.932,589.335L267.102,589.335L267.783,591.464L267.613,591.464L267.422,590.867ZM266.66,590.698L267.368,590.698L267.017,589.59L266.66,590.698Z" style="fill:rgb(167,0,231);fill-rule:nonzero;"/>
                <path d="M269.087,590.609L268.554,590.609L268.554,591.461L268.379,591.461L268.379,589.332L268.988,589.332C269.083,589.334 269.172,589.349 269.256,589.379C269.34,589.408 269.413,589.45 269.476,589.505C269.538,589.559 269.588,589.626 269.624,589.706C269.66,589.785 269.678,589.876 269.678,589.979C269.678,590.052 269.667,590.119 269.646,590.179C269.625,590.24 269.595,590.294 269.558,590.343C269.52,590.392 269.476,590.435 269.424,590.472C269.372,590.509 269.315,590.541 269.253,590.567L269.742,591.442L269.742,591.461L269.557,591.461L269.087,590.609ZM268.554,590.447L269.022,590.447C269.089,590.445 269.152,590.432 269.21,590.409C269.268,590.386 269.319,590.354 269.361,590.313C269.404,590.273 269.438,590.224 269.462,590.168C269.486,590.111 269.498,590.049 269.498,589.982C269.498,589.907 269.485,589.84 269.458,589.782C269.432,589.723 269.395,589.674 269.35,589.633C269.304,589.593 269.25,589.562 269.187,589.54C269.124,589.519 269.057,589.507 268.985,589.505L268.554,589.505L268.554,590.447Z" style="fill:rgb(0,255,0);fill-rule:nonzero;"/>
                <path d="M256.886,593.846C256.874,593.942 256.85,594.032 256.813,594.116C256.776,594.199 256.728,594.272 256.667,594.333C256.606,594.395 256.535,594.443 256.452,594.478C256.369,594.512 256.276,594.529 256.173,594.529C256.086,594.529 256.008,594.516 255.938,594.49C255.868,594.464 255.806,594.428 255.752,594.383C255.698,594.338 255.651,594.284 255.612,594.223C255.572,594.162 255.539,594.096 255.513,594.026C255.488,593.955 255.468,593.882 255.456,593.806C255.444,593.73 255.437,593.654 255.436,593.579L255.436,593.267C255.437,593.192 255.444,593.117 255.456,593.041C255.468,592.965 255.488,592.891 255.513,592.821C255.539,592.75 255.572,592.684 255.612,592.623C255.651,592.562 255.698,592.509 255.752,592.463C255.806,592.417 255.868,592.381 255.938,592.355C256.008,592.329 256.086,592.315 256.173,592.315C256.278,592.315 256.372,592.333 256.455,592.367C256.538,592.402 256.609,592.45 256.67,592.512C256.73,592.573 256.779,592.646 256.815,592.73C256.851,592.814 256.875,592.905 256.886,593.004L256.709,593.004C256.697,592.93 256.678,592.86 256.651,592.795C256.625,592.73 256.59,592.673 256.546,592.624C256.503,592.575 256.45,592.537 256.389,592.509C256.327,592.48 256.255,592.466 256.173,592.466C256.102,592.466 256.04,592.478 255.985,592.502C255.931,592.526 255.883,592.557 255.842,592.597C255.8,592.636 255.765,592.682 255.737,592.735C255.708,592.788 255.685,592.844 255.667,592.903C255.649,592.962 255.636,593.023 255.627,593.084C255.619,593.146 255.615,593.206 255.615,593.264L255.615,593.579C255.615,593.638 255.619,593.698 255.627,593.76C255.636,593.822 255.649,593.883 255.667,593.942C255.685,594.001 255.708,594.057 255.737,594.11C255.765,594.163 255.8,594.209 255.842,594.249C255.883,594.289 255.931,594.321 255.985,594.344C256.04,594.368 256.102,594.38 256.173,594.38C256.256,594.38 256.329,594.366 256.391,594.339C256.453,594.312 256.505,594.274 256.549,594.226C256.592,594.178 256.627,594.121 256.653,594.056C256.679,593.991 256.698,593.921 256.709,593.846L256.886,593.846Z" style="fill-rule:nonzero;"/>
                <path d="M258.672,593.573C258.671,593.647 258.665,593.722 258.654,593.799C258.643,593.876 258.625,593.95 258.602,594.022C258.578,594.094 258.548,594.161 258.512,594.224C258.476,594.288 258.433,594.343 258.383,594.39C258.333,594.437 258.276,594.474 258.211,594.501C258.146,594.529 258.073,594.542 257.993,594.542C257.913,594.542 257.841,594.529 257.776,594.501C257.711,594.474 257.653,594.437 257.602,594.39C257.552,594.343 257.508,594.287 257.471,594.224C257.435,594.16 257.405,594.093 257.381,594.021C257.357,593.949 257.34,593.874 257.328,593.798C257.317,593.722 257.31,593.647 257.309,593.573L257.309,593.287C257.31,593.214 257.317,593.139 257.328,593.062C257.34,592.985 257.357,592.911 257.38,592.839C257.404,592.767 257.433,592.699 257.47,592.635C257.507,592.572 257.55,592.516 257.6,592.469C257.65,592.421 257.708,592.384 257.772,592.356C257.837,592.329 257.909,592.315 257.99,592.315C258.071,592.315 258.143,592.329 258.208,592.356C258.273,592.384 258.331,592.421 258.382,592.468C258.432,592.515 258.476,592.57 258.512,592.634C258.548,592.697 258.578,592.765 258.602,592.837C258.625,592.909 258.643,592.984 258.654,593.06C258.665,593.137 258.671,593.213 258.672,593.287L258.672,593.573ZM258.508,593.284C258.507,593.227 258.503,593.167 258.495,593.104C258.488,593.042 258.476,592.981 258.459,592.921C258.442,592.862 258.421,592.805 258.394,592.751C258.368,592.697 258.335,592.649 258.297,592.609C258.259,592.568 258.215,592.536 258.164,592.512C258.113,592.488 258.055,592.476 257.99,592.476C257.925,592.476 257.867,592.488 257.817,592.513C257.766,592.537 257.722,592.569 257.684,592.61C257.646,592.651 257.613,592.698 257.587,592.752C257.561,592.806 257.539,592.863 257.522,592.923C257.506,592.982 257.494,593.043 257.486,593.106C257.479,593.168 257.474,593.228 257.474,593.284L257.474,593.573C257.474,593.631 257.479,593.691 257.487,593.753C257.495,593.815 257.507,593.876 257.524,593.936C257.54,593.996 257.562,594.053 257.588,594.107C257.615,594.161 257.647,594.208 257.685,594.249C257.723,594.29 257.768,594.322 257.819,594.347C257.87,594.371 257.928,594.383 257.993,594.383C258.059,594.383 258.117,594.371 258.167,594.347C258.218,594.322 258.262,594.29 258.3,594.249C258.338,594.208 258.371,594.161 258.397,594.107C258.423,594.053 258.445,593.996 258.461,593.936C258.477,593.876 258.489,593.815 258.496,593.753C258.503,593.691 258.507,593.631 258.508,593.573L258.508,593.284Z" style="fill:rgb(17,0,255);fill-rule:nonzero;"/>
                <path d="M260.247,593.933L259.395,593.933L259.199,594.529L259.012,594.529L259.741,592.401L259.908,592.401L260.63,594.529L260.446,594.529L260.247,593.933ZM259.471,593.763L260.195,593.763L259.833,592.644L259.471,593.763Z" style="fill:rgb(17,0,255);fill-rule:nonzero;"/>
                <path d="M261.226,594.359L262.333,594.359L262.333,594.529L261.056,594.529L261.056,592.401L261.226,592.401L261.226,594.359Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <path d="M262.843,592.401L264.12,592.401L264.12,592.571L263.611,592.571L263.611,594.359L264.12,594.359L264.12,594.529L262.843,594.529L262.843,594.359L263.44,594.359L263.44,592.571L262.843,592.571L262.843,592.401Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <path d="M266.08,592.571L265.4,592.571L265.4,594.529L265.228,594.529L265.23,592.571L264.547,592.571L264.547,592.401L266.08,592.401L266.08,592.571Z" style="fill:rgb(166,0,230);fill-rule:nonzero;"/>
                <path d="M266.506,592.401L267.783,592.401L267.783,592.571L267.272,592.571L267.272,594.359L267.783,594.359L267.783,594.529L266.506,594.529L266.506,594.359L267.102,594.359L267.102,592.571L266.506,592.571L266.506,592.401Z" style="fill:rgb(166,0,230);fill-rule:nonzero;"/>
                <path d="M269.657,593.601C269.656,593.674 269.649,593.749 269.638,593.825C269.626,593.902 269.607,593.976 269.582,594.047C269.557,594.119 269.525,594.186 269.487,594.249C269.448,594.311 269.403,594.366 269.35,594.413C269.297,594.46 269.236,594.497 269.167,594.524C269.098,594.551 269.021,594.565 268.935,594.565C268.85,594.565 268.774,594.551 268.705,594.524C268.635,594.497 268.574,594.46 268.52,594.413C268.467,594.366 268.42,594.311 268.381,594.248C268.342,594.185 268.31,594.117 268.285,594.046C268.26,593.974 268.241,593.901 268.229,593.825C268.217,593.749 268.21,593.674 268.209,593.601L268.209,593.317C268.21,593.244 268.217,593.169 268.229,593.093C268.241,593.016 268.26,592.942 268.285,592.871C268.309,592.799 268.341,592.732 268.38,592.669C268.419,592.606 268.465,592.55 268.518,592.503C268.571,592.456 268.632,592.418 268.701,592.391C268.769,592.364 268.846,592.351 268.932,592.351C269.018,592.351 269.095,592.364 269.164,592.391C269.233,592.418 269.295,592.455 269.348,592.502C269.402,592.549 269.448,592.604 269.487,592.667C269.525,592.731 269.557,592.798 269.582,592.869C269.607,592.941 269.626,593.015 269.638,593.091C269.649,593.168 269.656,593.243 269.657,593.317L269.657,593.601ZM269.482,593.314C269.481,593.257 269.477,593.197 269.469,593.135C269.461,593.073 269.448,593.012 269.431,592.953C269.413,592.894 269.39,592.837 269.362,592.784C269.334,592.73 269.299,592.683 269.259,592.642C269.219,592.602 269.171,592.57 269.117,592.546C269.063,592.522 269.001,592.511 268.932,592.511C268.863,592.511 268.802,592.523 268.748,592.547C268.694,592.571 268.647,592.603 268.607,592.644C268.566,592.684 268.532,592.731 268.504,592.785C268.476,592.839 268.453,592.895 268.435,592.954C268.418,593.014 268.405,593.074 268.397,593.137C268.389,593.199 268.385,593.258 268.384,593.314L268.384,593.601C268.385,593.658 268.389,593.718 268.398,593.78C268.406,593.841 268.419,593.902 268.437,593.962C268.455,594.021 268.478,594.078 268.506,594.132C268.534,594.186 268.568,594.233 268.608,594.273C268.649,594.314 268.696,594.346 268.75,594.37C268.805,594.394 268.866,594.406 268.935,594.406C269.005,594.406 269.067,594.394 269.121,594.37C269.174,594.346 269.222,594.314 269.262,594.273C269.302,594.233 269.337,594.186 269.365,594.132C269.393,594.078 269.416,594.021 269.433,593.962C269.45,593.902 269.462,593.841 269.47,593.78C269.477,593.718 269.481,593.658 269.482,593.601L269.482,593.314Z" style="fill:rgb(0,255,0);fill-rule:nonzero;"/>
                <path d="M271.36,594.529L271.192,594.529L270.252,592.732L270.25,594.529L270.083,594.529L270.083,592.401L270.25,592.401L271.191,594.195L271.192,592.401L271.36,592.401L271.36,594.529Z" style="fill:rgb(0,255,0);fill-rule:nonzero;"/>
                <g transform="matrix(1,0,0,1,-0.0851546,0.785513)">
                    <path d="M272.339,593.416C272.34,593.378 272.348,593.342 272.361,593.309C272.374,593.277 272.393,593.248 272.417,593.224C272.441,593.201 272.469,593.182 272.503,593.169C272.537,593.156 272.574,593.15 272.615,593.15C272.655,593.15 272.693,593.156 272.726,593.169C272.76,593.182 272.789,593.201 272.814,593.224C272.838,593.248 272.857,593.277 272.87,593.309C272.883,593.342 272.891,593.378 272.892,593.416L272.892,593.48C272.891,593.519 272.883,593.555 272.87,593.587C272.857,593.619 272.838,593.647 272.814,593.67C272.79,593.694 272.762,593.712 272.728,593.725C272.694,593.737 272.657,593.744 272.616,593.744C272.576,593.744 272.538,593.737 272.505,593.725C272.471,593.712 272.442,593.694 272.418,593.67C272.393,593.647 272.374,593.619 272.361,593.587C272.348,593.555 272.34,593.519 272.339,593.48L272.339,593.416Z" style="fill:rgb(0,254,255);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-0.170309,0)">
                    <path d="M274.851,593.936L274.027,593.936L273.839,594.529L273.659,594.529L274.365,592.401L274.525,592.401L275.225,594.529L275.046,594.529L274.851,593.936ZM274.076,593.763L274.8,593.763L274.445,592.659L274.076,593.763Z" style="fill:rgb(0,254,255);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-0.0851546,0)">
                    <path d="M276.409,593.678L275.873,593.678L275.873,594.529L275.703,594.529L275.703,592.401L276.31,592.401C276.405,592.403 276.494,592.418 276.578,592.446C276.661,592.474 276.734,592.515 276.797,592.568C276.86,592.621 276.909,592.686 276.945,592.763C276.981,592.84 276.999,592.928 276.999,593.028C276.999,593.099 276.988,593.164 276.967,593.222C276.946,593.281 276.916,593.334 276.879,593.381C276.842,593.428 276.797,593.47 276.745,593.506C276.694,593.542 276.637,593.573 276.575,593.598L277.065,594.51L277.065,594.529L276.881,594.529L276.409,593.678ZM275.873,593.508L276.342,593.508C276.408,593.506 276.47,593.493 276.528,593.47C276.586,593.447 276.636,593.415 276.678,593.375C276.721,593.334 276.754,593.286 276.778,593.23C276.802,593.174 276.814,593.112 276.814,593.045C276.814,592.97 276.801,592.904 276.774,592.846C276.748,592.788 276.712,592.738 276.667,592.698C276.621,592.658 276.567,592.627 276.505,592.606C276.443,592.584 276.377,592.573 276.305,592.571L275.873,592.571L275.873,593.508Z" style="fill:rgb(249,255,0);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-0.0851546,0)">
                    <path d="M278.853,592.571L278.173,592.571L278.173,594.529L278.001,594.529L278.003,592.571L277.321,592.571L277.321,592.401L278.853,592.401L278.853,592.571Z" style="fill:rgb(249,255,0);fill-rule:nonzero;"/>
                </g>
            </g>
        </svg>
        <svg
            class="awc-banner-medium"
            width="100%" height="100%"
            viewBox="0 0 128 96"
            version="1.1" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            xml:space="preserve"
            xmlns:serif="http://www.serif.com/"
            style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
            <g transform="matrix(0.857143,0,0,0.857143,-43,-266.571)">
                <g>
                    <g transform="matrix(0.585105,0,0,0.280697,1.22742,269.452)">
                        <rect x="255.121" y="148.017" width="85.74" height="315.881" style="fill:rgb(1,255,1);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,2.35969e-05,269.452)">
                        <rect x="170.079" y="148.017" width="85.039" height="315.881" style="fill:rgb(0,254,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.28439,4.44527e-06,268.905)">
                        <rect x="-0" y="148.018" width="85.039" height="315.881" style="fill:rgb(204,204,204);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.280697,5.89923e-06,269.452)">
                        <rect x="85.039" y="148.017" width="85.039" height="315.881" style="fill:rgb(255,255,1);"/>
                    </g>
                    <g transform="matrix(0.592782,0,0,0.280697,-2.384,269.452)">
                        <rect x="340.57" y="148.017" width="84.629" height="315.881" style="fill:rgb(255,0,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,50.1667,388.733)">
                        <rect x="170.079" y="148.017" width="85.039" height="315.881" style="fill:rgb(255,0,254);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,50.1667,388.733)">
                        <rect x="-0" y="148.018" width="85.039" height="315.881" style="fill:rgb(3,38,204);"/>
                    </g>
                    <g transform="matrix(0.589923,0,0,0.0738675,50.1667,388.733)">
                        <rect x="85.039" y="148.017" width="85.039" height="315.881" style="fill:rgb(24,24,24);"/>
                    </g>
                </g>
            </g>
            <g id="_-anti-war--coalition-art-" serif:id="(anti•war ¶coalition•art)" transform="matrix(11.7433,0,0,11.7433,-2996.67,-6911.76)">
                <path d="M256.522,590.867L255.715,590.867L255.527,591.461L255.351,591.461L256.041,589.332L256.199,589.332L256.884,591.461L256.709,591.461L256.522,590.867ZM255.764,590.698L256.472,590.698L256.12,589.591L255.764,590.698Z" style="fill:rgb(15,0,255);fill-rule:nonzero;"/>
                <path d="M258.672,591.464L258.502,591.464L257.48,589.676L257.48,591.464L257.309,591.464L257.309,589.335L257.48,589.335L258.502,591.126L258.502,589.335L258.672,589.335L258.672,591.464Z" style="fill:rgb(16,0,255);fill-rule:nonzero;"/>
                <path d="M260.545,589.505L259.865,589.505L259.865,591.464L259.694,591.464L259.694,589.505L259.012,589.505L259.012,589.335L260.545,589.335L260.545,589.505Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <path d="M261.141,589.335L262.419,589.335L262.419,589.505L261.908,589.505L261.908,591.294L262.419,591.294L262.419,591.461L261.141,591.461L261.141,591.294L261.737,591.294L261.737,589.505L261.141,589.505L261.141,589.335Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                <g transform="matrix(1,0,0,1,-0.239225,-0.00332417)">
                    <path d="M263.192,590.368C263.193,590.329 263.2,590.294 263.214,590.261C263.227,590.228 263.246,590.2 263.27,590.176C263.294,590.152 263.322,590.134 263.356,590.121C263.39,590.108 263.427,590.101 263.468,590.101C263.508,590.101 263.546,590.108 263.579,590.121C263.613,590.134 263.642,590.152 263.666,590.176C263.691,590.2 263.71,590.228 263.723,590.261C263.736,590.294 263.744,590.329 263.745,590.368L263.745,590.432C263.744,590.471 263.736,590.506 263.723,590.538C263.71,590.571 263.691,590.598 263.667,590.622C263.643,590.645 263.615,590.663 263.581,590.676C263.547,590.689 263.51,590.695 263.469,590.695C263.428,590.695 263.391,590.689 263.358,590.676C263.324,590.663 263.295,590.645 263.27,590.622C263.246,590.598 263.227,590.571 263.214,590.538C263.2,590.506 263.193,590.471 263.192,590.432L263.192,590.368Z" style="fill:rgb(166,1,230);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-0.376982,0.00184799)">
                    <path d="M264.937,590.978L264.949,591.127L264.966,590.978L265.28,589.332L265.434,589.332L265.748,590.978L265.766,591.132L265.779,590.978L266,589.332L266.165,589.332L265.862,591.461L265.686,591.461L265.357,589.686L265.027,591.461L264.851,591.461L264.547,589.332L264.715,589.332L264.937,590.978Z" style="fill:rgb(166,1,230);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-10.898,3.06832)">
                    <path d="M267.422,590.867L266.615,590.867L266.421,591.464L266.251,591.464L266.932,589.335L267.102,589.335L267.783,591.464L267.613,591.464L267.422,590.867ZM266.66,590.698L267.368,590.698L267.017,589.59L266.66,590.698Z" style="fill:rgb(15,0,255);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-11.0725,3.07145)">
                    <path d="M269.087,590.609L268.554,590.609L268.554,591.461L268.379,591.461L268.379,589.332L268.988,589.332C269.083,589.334 269.172,589.349 269.256,589.379C269.34,589.408 269.413,589.45 269.476,589.505C269.538,589.559 269.588,589.626 269.624,589.706C269.66,589.785 269.678,589.876 269.678,589.979C269.678,590.052 269.667,590.119 269.646,590.179C269.625,590.24 269.595,590.294 269.558,590.343C269.52,590.392 269.476,590.435 269.424,590.472C269.372,590.509 269.315,590.541 269.253,590.567L269.742,591.442L269.742,591.461L269.557,591.461L269.087,590.609ZM268.554,590.447L269.022,590.447C269.089,590.445 269.152,590.432 269.21,590.409C269.268,590.386 269.319,590.354 269.361,590.313C269.404,590.273 269.438,590.224 269.462,590.168C269.486,590.111 269.498,590.049 269.498,589.982C269.498,589.907 269.485,589.84 269.458,589.782C269.432,589.723 269.395,589.674 269.35,589.633C269.304,589.593 269.25,589.562 269.187,589.54C269.124,589.519 269.057,589.507 268.985,589.505L268.554,589.505L268.554,590.447Z" style="fill:rgb(15,0,255);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-12.8363,0.788271)">
                    <path d="M272.339,593.416C272.34,593.378 272.348,593.342 272.361,593.309C272.374,593.277 272.393,593.248 272.417,593.224C272.441,593.201 272.469,593.182 272.503,593.169C272.537,593.156 272.574,593.15 272.615,593.15C272.655,593.15 272.693,593.156 272.726,593.169C272.76,593.182 272.789,593.201 272.814,593.224C272.838,593.248 272.857,593.277 272.87,593.309C272.883,593.342 272.891,593.378 272.892,593.416L272.892,593.48C272.891,593.519 272.883,593.555 272.87,593.587C272.857,593.619 272.838,593.647 272.814,593.67C272.79,593.694 272.762,593.712 272.728,593.725C272.694,593.737 272.657,593.744 272.616,593.744C272.576,593.744 272.538,593.737 272.505,593.725C272.471,593.712 272.442,593.694 272.418,593.67C272.393,593.647 272.374,593.619 272.361,593.587C272.348,593.555 272.34,593.519 272.339,593.48L272.339,593.416Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-12.8607,0)">
                    <path d="M274.851,593.936L274.027,593.936L273.839,594.529L273.659,594.529L274.365,592.401L274.525,592.401L275.225,594.529L275.046,594.529L274.851,593.936ZM274.076,593.763L274.8,593.763L274.445,592.659L274.076,593.763Z" style="fill:rgb(177,0,0);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-12.944,0)">
                    <path d="M276.409,593.678L275.873,593.678L275.873,594.529L275.703,594.529L275.703,592.401L276.31,592.401C276.405,592.403 276.494,592.418 276.578,592.446C276.661,592.474 276.734,592.515 276.797,592.568C276.86,592.621 276.909,592.686 276.945,592.763C276.981,592.84 276.999,592.928 276.999,593.028C276.999,593.099 276.988,593.164 276.967,593.222C276.946,593.281 276.916,593.334 276.879,593.381C276.842,593.428 276.797,593.47 276.745,593.506C276.694,593.542 276.637,593.573 276.575,593.598L277.065,594.51L277.065,594.529L276.881,594.529L276.409,593.678ZM275.873,593.508L276.342,593.508C276.408,593.506 276.47,593.493 276.528,593.47C276.586,593.447 276.636,593.415 276.678,593.375C276.721,593.334 276.754,593.286 276.778,593.23C276.802,593.174 276.814,593.112 276.814,593.045C276.814,592.97 276.801,592.904 276.774,592.846C276.748,592.788 276.712,592.738 276.667,592.698C276.621,592.658 276.567,592.627 276.505,592.606C276.443,592.584 276.377,592.573 276.305,592.571L275.873,592.571L275.873,593.508Z" style="fill:rgb(166,1,230);fill-rule:nonzero;"/>
                </g>
                <g transform="matrix(1,0,0,1,-12.944,0)">
                    <path d="M278.853,592.571L278.173,592.571L278.173,594.529L278.001,594.529L278.003,592.571L277.321,592.571L277.321,592.401L278.853,592.401L278.853,592.571Z" style="fill:rgb(166,1,230);fill-rule:nonzero;"/>
                </g>
            </g>
        </svg>
    </a>
</template>

<script>
export default {
  name: 'AntiWarCoalitionBanner'
}
</script>
<style lang="scss" scoped>
.anti-war-coalition-banner {
    display: block;
    .awc-banner-full {
        display: none;
    }
    .awc-banner-medium {
        display: block;
    }
    @media (min-width: 536px) {
        .awc-banner-full {
            display: block;
        }
        .awc-banner-medium {
            display: none;
        }
    }
}
</style>
